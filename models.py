"""
Models for the Panel Planner application.
"""
from PyQt6.QtCore import Qt, QPointF, QRectF
from PyQt6.QtGui import QPainter, QColor, QBrush, QPen
from PyQt6.QtWidgets import QGraphicsRectItem, QGraphicsItem, QGraphicsLineItem


class Panel(QGraphicsRectItem):
    """
    Represents a panel in the workspace.
    """
    def __init__(self, panel_id, width, height, color=QColor(100, 180, 255, 200), duplicate_level=0, order=None):
        """
        Initialize a panel with the given dimensions.

        Args:
            panel_id (str): Unique identifier for the panel
            width (float): Width of the panel in mm
            height (float): Height of the panel in mm
            color (QColor): Color of the panel
            duplicate_level (int): Level of duplication (0=original, 1=first duplicate, 2+=additional duplicates)
            order (int, optional): Pořadí panelu (zobrazeno v červené kružnici)
        """
        # Otočeno o 90 stupňů - prohozená šířka a výška
        super().__init__(0, 0, height, width)
        self.panel_id = panel_id
        self.original_width = width
        self.original_height = height
        self.duplicate_level = duplicate_level
        self.is_duplicate = duplicate_level > 0  # Pro zpětnou kompatibilitu
        self.order = order  # Pořadí panelu

        # Barvy pro různé stavy panelu
        self.color = QColor(100, 180, 255, 200)  # Základní modrá barva
        self.duplicate_color = QColor(255, 165, 0, 200)  # Oranžová barva pro první duplikát
        self.multi_duplicate_color = QColor(255, 0, 255, 200)  # Fialová barva pro další duplikáty
        self.collision_color = QColor(255, 100, 100, 200)  # Červená barva pro kolize
        self.border_color = QColor(40, 40, 40)  # Tmavá barva pro okraj
        self.text_color = QColor(255, 255, 255)  # Bílá barva pro text
        self.order_color = QColor(255, 0, 0)  # Červená barva pro pořadí
        self.snap_color = QColor(0, 255, 0, 200)  # Zelená barva pro indikaci snapování
        self.is_snapping = False  # Indikátor, zda panel právě snapuje

        # Set flags for interaction
        self.setFlag(QGraphicsRectItem.GraphicsItemFlag.ItemIsMovable)
        self.setFlag(QGraphicsRectItem.GraphicsItemFlag.ItemIsSelectable)
        self.setFlag(QGraphicsRectItem.GraphicsItemFlag.ItemSendsGeometryChanges)
        self.setFlag(QGraphicsRectItem.GraphicsItemFlag.ItemIsFocusable)  # Umožní panelu přijímat focus
        self.setAcceptHoverEvents(True)

        # Initialize position
        self.setPos(0, 0)

    def hoverEnterEvent(self, event):
        """Change cursor when hovering over the panel."""
        self.setCursor(Qt.CursorShape.SizeAllCursor)
        super().hoverEnterEvent(event)

    def hoverLeaveEvent(self, event):
        """Reset cursor when leaving the panel."""
        self.setCursor(Qt.CursorShape.ArrowCursor)
        super().hoverLeaveEvent(event)

    def mouseMoveEvent(self, event):
        """
        Handle mouse move events for showing snap indicator during dragging.

        Args:
            event (QGraphicsSceneMouseEvent): Mouse event
        """
        # Nastavíme indikátor snapování během přesouvání
        self.is_snapping = True
        self.update()  # Vynutíme překreslení panelu

        # Předáme událost nadřazené třídě
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """
        Handle mouse release events for resetting snap indicator after dragging.

        Args:
            event (QGraphicsSceneMouseEvent): Mouse event
        """
        # Resetujeme indikátor snapování po dokončení přesouvání
        self.is_snapping = False
        self.update()  # Vynutíme překreslení panelu

        # Předáme událost nadřazené třídě
        super().mouseReleaseEvent(event)

    def paint(self, painter: QPainter, option=None, widget=None):
        """
        Paint the panel with custom appearance.

        Args:
            painter (QPainter): Painter object
            option: Style options (not used)
            widget: Widget being painted on (not used)
        """
        # Set brush based on selection, snap or duplicate state
        if self.isSelected():
            # Pokud je panel vybrán, použijeme výraznější modrou barvu
            selection_color = QColor(50, 150, 255, 230)  # Výraznější modrá pro vybraný panel
            painter.setBrush(QBrush(selection_color))
        elif self.is_snapping:
            # Pokud panel snapuje, použijeme zelenou barvu
            painter.setBrush(QBrush(self.snap_color))
        elif self.duplicate_level >= 2:
            # Pokud je panel třetí nebo další duplikát, použijeme fialovou barvu
            painter.setBrush(QBrush(self.multi_duplicate_color))
        elif self.duplicate_level == 1:
            # Pokud je panel první duplikát, použijeme oranžovou barvu
            painter.setBrush(QBrush(self.duplicate_color))
        else:
            # Jinak použijeme základní modrou barvu
            painter.setBrush(QBrush(self.color))

        # Set pen for border - silnější okraj pro vybraný panel
        if self.isSelected():
            painter.setPen(QPen(QColor(0, 100, 255), 3))  # Tmavší modrá, silnější okraj pro vybraný panel
        elif self.duplicate_level >= 2:
            painter.setPen(QPen(QColor(180, 0, 180), 2))  # Tmavší fialová pro další duplikáty
        elif self.duplicate_level == 1:
            painter.setPen(QPen(QColor(200, 100, 0), 2))  # Tmavší oranžová pro první duplikát
        else:
            painter.setPen(QPen(self.border_color, 2))

        # Draw rectangle
        painter.drawRect(self.rect())

        # Uložíme aktuální transformaci
        painter.save()

        # Nastavíme extrémně velké písmo (10x větší)
        font = painter.font()
        font.setPointSize(100)  # 10x větší písmo

        # Nastavíme černou barvu textu
        painter.setPen(QColor(0, 0, 0))  # Černá barva

        # Otočíme celý kontext malování o 360 stupňů (0 stupňů)
        # a posuneme ho na správné místo pro text podél délky panelu
        # Umístíme text mírně pod střed panelu
        painter.translate(self.rect().width() / 2, self.rect().height() / 2 + 30)
        painter.rotate(0)  # Žádná rotace (nebo 360 stupňů)

        # Připravíme texty - změníme formát rozměrů
        dim_text = f"{int(self.original_width)}×{int(self.original_height)}"

        # Nejprve vykreslíme ID panelu tučným písmem
        font.setBold(True)
        painter.setFont(font)
        # Ještě více zvětšíme mezeru mezi ID a rozměry - posuneme ID ještě více doleva
        id_point = QPointF(-250, 0)
        painter.drawText(id_point, self.panel_id)

        # Poté vykreslíme rozměry normálním (ne tučným) písmem
        font.setBold(False)
        painter.setFont(font)
        # Ještě více zvětšíme mezeru mezi ID a rozměry - posuneme rozměry ještě více doprava
        dim_point = QPointF(250, 0)
        painter.drawText(dim_point, dim_text)

        # Obnovíme původní transformaci
        painter.restore()

        # Pokud má panel pořadí, vykreslíme ho v červené kružnici
        if self.order is not None:
            # Velikost kružnice - přizpůsobíme přesně tloušťce (výšce) panelu
            panel_height = self.rect().height()
            circle_size = panel_height  # Přesně stejná velikost jako tloušťka panelu

            # Nastavíme barvu a pero pro kružnici - červená kružnice s červenou výplní
            border_width = max(2, int(circle_size * 0.05))  # 5% velikosti kružnice, minimálně 2 pixely
            painter.setPen(QPen(self.order_color, border_width))
            painter.setBrush(QBrush(self.order_color))  # Červená výplň

            # Pozice kružnice - pravý dolní roh
            circle_x = self.rect().width() - circle_size - 10
            circle_y = self.rect().height() - circle_size - 10

            # Vykreslíme kružnici - použijeme QRectF pro správné předání parametrů
            painter.drawEllipse(QRectF(circle_x, circle_y, circle_size, circle_size))

            # Nastavíme barvu a font pro text - bílý text
            painter.setPen(QColor(255, 255, 255))  # Bílá barva pro text
            font = painter.font()
            font.setBold(True)
            font.setPointSize(int(circle_size * 0.6))  # 60% velikosti kružnice
            painter.setFont(font)

            # Vykreslíme pořadí do kružnice - použijeme QRectF pro správné předání parametrů
            order_text = str(self.order)
            painter.drawText(QRectF(circle_x, circle_y, circle_size, circle_size),
                            Qt.AlignmentFlag.AlignCenter, order_text)

    def itemChange(self, change, value):
        """
        Handle item changes, including position changes for snapping.

        Args:
            change: Type of change
            value: New value

        Returns:
            Modified value
        """
        # Pokud se mění pozice panelu
        if change == QGraphicsItem.GraphicsItemChange.ItemPositionChange and self.scene():
            # Získáme novou pozici
            new_pos = value

            # Definujeme práh pro snapování (v pixelech) - zvětšený radius
            snap_threshold = 50

            # Získáme všechny panely ve scéně kromě tohoto
            all_panels = [item for item in self.scene().items()
                         if isinstance(item, Panel) and item != self]

            # Pokud nejsou žádné panely, vrátíme původní hodnotu
            if not all_panels:
                return new_pos

            # Získáme obdélník tohoto panelu v souřadnicích scény
            my_rect = self.sceneBoundingRect().translated(new_pos - self.pos())

            # Definujeme rohy a hrany tohoto panelu
            my_corners = [
                my_rect.topLeft(),
                my_rect.topRight(),
                my_rect.bottomLeft(),
                my_rect.bottomRight()
            ]

            my_edges = [
                (my_rect.left(), my_rect.right(), my_rect.top(), "top"),
                (my_rect.left(), my_rect.right(), my_rect.bottom(), "bottom"),
                (my_rect.top(), my_rect.bottom(), my_rect.left(), "left"),
                (my_rect.top(), my_rect.bottom(), my_rect.right(), "right")
            ]

            # Nejlepší snap a jeho vzdálenost
            best_snap = None
            best_distance = float('inf')

            # Nejprve zkontrolujeme snapování rohů (priorita)
            for panel in all_panels:
                # Získáme obdélník druhého panelu
                other_rect = panel.sceneBoundingRect()

                # Definujeme rohy druhého panelu
                other_corners = [
                    other_rect.topLeft(),
                    other_rect.topRight(),
                    other_rect.bottomLeft(),
                    other_rect.bottomRight()
                ]

                # Zkontrolujeme všechny kombinace rohů
                for my_corner in my_corners:
                    for other_corner in other_corners:
                        # Vypočítáme vzdálenost mezi rohy
                        distance = ((my_corner.x() - other_corner.x()) ** 2 +
                                   (my_corner.y() - other_corner.y()) ** 2) ** 0.5

                        # Pokud je vzdálenost menší než práh a lepší než dosavadní nejlepší
                        if distance < snap_threshold and distance < best_distance:
                            # Vypočítáme posun, který by byl potřeba pro snapnutí
                            offset = other_corner - my_corner
                            best_snap = new_pos + offset
                            best_distance = distance

            # Pokud jsme nenašli žádný vhodný roh, zkontrolujeme hrany
            if best_snap is None:
                for panel in all_panels:
                    other_rect = panel.sceneBoundingRect()

                    # Definujeme hrany druhého panelu
                    other_edges = [
                        (other_rect.left(), other_rect.right(), other_rect.top(), "top"),
                        (other_rect.left(), other_rect.right(), other_rect.bottom(), "bottom"),
                        (other_rect.top(), other_rect.bottom(), other_rect.left(), "left"),
                        (other_rect.top(), other_rect.bottom(), other_rect.right(), "right")
                    ]

                    # Zkontrolujeme všechny kombinace hran
                    for _, _, my_pos, my_type in my_edges:
                        for _, _, other_pos, other_type in other_edges:
                            # Snapujeme pouze hrany, které jsou rovnoběžné
                            if (my_type in ["top", "bottom"] and other_type in ["top", "bottom"]) or \
                               (my_type in ["left", "right"] and other_type in ["left", "right"]):

                                # Vypočítáme vzdálenost mezi hranami
                                distance = abs(my_pos - other_pos)

                                # Pokud je vzdálenost menší než práh a lepší než dosavadní nejlepší
                                if distance < snap_threshold and distance < best_distance:
                                    # Vypočítáme posun, který by byl potřeba pro snapnutí
                                    if my_type in ["top", "bottom"]:
                                        offset = QPointF(0, other_pos - my_pos)
                                    else:  # left, right
                                        offset = QPointF(other_pos - my_pos, 0)

                                    best_snap = new_pos + offset
                                    best_distance = distance

            # Pokud jsme našli vhodný snap, použijeme ho
            if best_snap is not None:
                # Nastavíme indikátor snapování pouze během přesouvání, ne po dokončení snapu
                # self.is_snapping = True
                # self.update()  # Vynutíme překreslení panelu
                return best_snap

            # Pokud jsme nenašli žádný snap, resetujeme indikátor
            self.is_snapping = False
            self.update()  # Vynutíme překreslení panelu
            return new_pos

        # Pokud se mění pozice panelu (dokončení přesunu)
        elif change == QGraphicsItem.GraphicsItemChange.ItemPositionHasChanged:
            # Resetujeme indikátor snapování po dokončení přesunu
            self.is_snapping = False
            self.update()  # Vynutíme překreslení panelu

        # Pro ostatní změny použijeme výchozí chování
        return super().itemChange(change, value)


class PanelLibrary:
    """
    Manages the collection of available panels.
    """
    def __init__(self):
        """Initialize the panel library with default panels."""
        # Panely pro 1NP
        self.panels_1np = {
            "O1.08": (280, 6519), "O1.09": (280, 10017), "P1.69": (125, 2544),
            "P1.66": (210, 8666), "P1.62": (125, 3419), "P1.65": (125, 1193),
            "P1.64": (125, 1034), "P1.63": (125, 1829), "P1.61": (125, 1034),
            "O1.06": (280, 1590), "O1.05": (280, 3101), "O1.07": (280, 636),
            "P1.70": (210, 6122), "P1.68": (125, 2783), "P1.67": (125, 2226),
            "P1.57": (210, 10415), "P1.36": (125, 3657), "P1.35": (125, 1034),
            "P1.34": (210, 2544), "P1.33": (210, 3021), "O1.10": (280, 5009),
            "O1.11": (280, 7791), "P1.58": (210, 1908), "P1.60": (210, 2306),
            "P1.71": (210, 6360), "P1.73": (125, 1829), "P1.74": (125, 1829),
            "P1.72": (125, 3975), "P1.75": (125, 3975), "P1.76": (210, 3975),
            "P1.59": (210, 8507), "P1.39": (210, 5883), "P1.40": (210, 9461)
        }

        # Panely pro 2NP - nové panely ze souboru 2np.txt
        self.panels_2np = {
            "O2.01": (280, 9980), "O2.02": (280, 1476), "O2.03": (280, 9600),
            "O2.04": (280, 9740), "P2.05": (210, 6450), "P2.06": (125, 4950),
            "P2.07": (125, 3975), "P2.08": (125, 975), "P2.09": (125, 975),
            "P2.10": (125, 975), "P2.11": (210, 4950), "P2.12": (210, 5475),
            "P2.13": (210, 5100), "O2.14": (280, 8775), "P2.15": (210, 2100),
            "P2.16": (210, 4950), "O2.17": (280, 3000), "P2.18": (210, 7650),
            "O2.19": (280, 1950), "P2.20": (210, 2325), "O2.21": (280, 10875),
            "O2.22": (280, 9980), "O3.01": (280, 9980), "O3.02": (280, 9980),
            "O3.03": (210, 9980)
        }

        # Stropy pro 1NP
        self.stropy_1np = {
            "ST-25": (320, 9512), "ST-24": (320, 9512), "ST-23": (320, 9512),
            "ST-22": (320, 9512), "ST-21": (320, 9512), "ST-20": (320, 6300),
            "ST-19": (320, 6300), "ST-18": (320, 6300), "ST-17": (320, 6300),
            "ST-26": (320, 3000), "ST-XX": (320, 7300)
        }

        # Stropy pro 2NP
        self.stropy_2np = {
            "S-1": (320, 9512), "S-2": (320, 9512), "S-3": (320, 4400),
            "S-4": (320, 3000), "S-5": (320, 4700), "S-6": (320, 4700),
            "S-7": (320, 4700), "S-8": (320, 4700), "S-9": (320, 4700),
            "S-10": (320, 4700), "S-11": (320, 4700), "S-12": (320, 4700)
        }

        # Panely pro střechu
        self.strecha = {
            "STR-1": (300, 7100), "STR-2": (300, 7100), "STR-3": (300, 7100),
            "STR-4": (300, 7100), "STR-5": (300, 7100), "STR-6": (300, 7100),
            "STR-7": (300, 7100), "STR-8": (300, 7100), "STR-9": (300, 7100),
            "STR-10": (300, 7100), "STR-11": (300, 7100), "STR-12": (300, 7100),
            "STR-13": (300, 7100), "STR-14": (300, 7100), "STR-15": (300, 7100),
            "STR-16": (300, 7100), "STR-17": (300, 7100), "STR-18": (300, 7100)
        }

        # Panely 2část
        self.panels_2cast = {
            "P1-24": (125, 2075), "P1-25": (125, 3605), "P1-26": (185, 4151),
            "P1-27": (125, 2512), "P1-28": (185, 11469), "P1-29": (125, 2622),
            "P1-30": (125, 3495), "P1-31": (125, 1966), "P1-32": (125, 3714),
            "P1-37": (185, 11360), "P1-38": (185, 11469), "P1-41": (125, 5898),
            "P1-42": (125, 5898), "P1-43": (125, 1529), "P1-44": (125, 1857),
            "P1-45": (125, 5898), "P1-46": (125, 2730), "P1-47": (125, 5898),
            "P1-49": (185, 5898), "P1-50": (125, 874), "P1-51": (125, 1529),
            "P1-52": (125, 5898), "P1-53": (125, 2403), "P1-54": (185, 11797),
            "P1-55": (185, 5898), "P1-56": (185, 11469), "O1-04": (265, 11142),
            "O1-03": (265, 11688), "O1-02": (265, 10486), "O1-01": (265, 10049),
            "O1-23": (265, 2840), "O1-22": (265, 10923), "O1-21": (265, 11142)
        }

        # Výchozí panely (pro zpětnou kompatibilitu)
        self.panels = self.panels_2cast.copy()

    def get_panel_ids(self, floor=None):
        """
        Get a list of all panel IDs.

        Args:
            floor (str, optional): Floor to get panels for ('1NP', '2NP', or 'Stropy 1NP').
                                  If None, returns panels from the current selection.

        Returns:
            list: List of panel IDs sorted by number in the name
        """
        # Funkce pro extrakci čísla z ID panelu
        def extract_number(panel_id):
            # Rozdělíme ID na prefix a číslo
            import re
            # Pro panely typu O nebo P
            match = re.match(r'([OP])(\d+)\.(\d+)', panel_id)
            if match:
                prefix, floor_num, panel_num = match.groups()
                # Vrátíme tuple (číslo patra, číslo panelu) pro řazení
                return (int(floor_num), int(panel_num))

            # Pro stropy typu ST-XX
            match = re.match(r'ST-(\d+|XX)', panel_id)
            if match:
                strop_num = match.group(1)
                if strop_num == 'XX':
                    return (999, 999)  # Speciální případ pro ST-XX
                return (0, int(strop_num))

            # Pro stropy typu S-X
            match = re.match(r'S-(\d+)', panel_id)
            if match:
                strop_num = match.group(1)
                return (1, int(strop_num))

            # Pro panely střechy typu STR-X
            match = re.match(r'STR-(\d+)', panel_id)
            if match:
                strecha_num = match.group(1)
                return (2, int(strecha_num))

            return (0, 0)  # Výchozí hodnota pro neplatné ID

        # Získáme panely podle patra a seřadíme je podle čísla
        if floor == '1NP':
            return sorted(self.panels_1np.keys(), key=extract_number)
        elif floor == 'Panely 2část':
            return sorted(self.panels_2cast.keys(), key=extract_number)
        elif floor == '2NP':
            return sorted(self.panels_2np.keys(), key=extract_number)
        elif floor == 'Stropy 1NP':
            return sorted(self.stropy_1np.keys(), key=extract_number)
        elif floor == 'Stropy 2NP':
            return sorted(self.stropy_2np.keys(), key=extract_number)
        elif floor == 'Střecha':
            return sorted(self.strecha.keys(), key=extract_number)
        elif floor == 'Panely 2část':
            return sorted(self.panels_2cast.keys(), key=extract_number)
        else:
            return sorted(self.panels.keys(), key=extract_number)

    def get_panel_dimensions(self, panel_id):
        """
        Get the dimensions of a panel.

        Args:
            panel_id (str): ID of the panel

        Returns:
            tuple: (width, height) in mm
        """
        # Nejprve zkusíme najít panel v aktuální sadě
        if panel_id in self.panels:
            return self.panels[panel_id]

        # Pokud není v aktuální sadě, zkusíme 1NP
        if panel_id in self.panels_1np:
            return self.panels_1np[panel_id]

        # Pokud není ani v 1NP, zkusíme 2NP
        if panel_id in self.panels_2np:
            return self.panels_2np[panel_id]

        # Pokud není ani v 2NP, zkusíme stropy 1NP
        if panel_id in self.stropy_1np:
            return self.stropy_1np[panel_id]

        # Pokud není ani ve stropech 1NP, zkusíme stropy 2NP
        if panel_id in self.stropy_2np:
            return self.stropy_2np[panel_id]
        
        # Pokud není ani ve stropech 2NP, zkusíme Panely 2cast
        if panel_id in self.panels_2cast:
            return self.panels_2cast[panel_id]

        # Pokud není ani ve stropech 2NP, zkusíme střechu
        if panel_id in self.strecha:
            return self.strecha[panel_id]

        # Pokud není ani ve střeše, zkusíme Panely 2část
        if panel_id in self.panels_2cast:
            return self.panels_2cast[panel_id]

        # Pokud panel nenajdeme nikde, vrátíme výchozí hodnoty
        return (0, 0)

    def create_panel(self, panel_id, duplicate_level=0):
        """
        Create a new Panel instance.

        Args:
            panel_id (str): ID of the panel to create
            duplicate_level (int): Level of duplication (0=original, 1=first duplicate, 2+=additional duplicates)

        Returns:
            Panel: New Panel instance or None if panel_id is invalid
        """
        # Získáme rozměry panelu
        width, height = self.get_panel_dimensions(panel_id)

        # Pokud jsou rozměry nenulové, vytvoříme panel
        if width > 0 and height > 0:
            return Panel(panel_id, width, height, duplicate_level=duplicate_level)

        return None

    def set_floor(self, floor):
        """
        Nastaví aktuální patro pro knihovnu.

        Args:
            floor (str): Patro ('1NP', '2NP', 'Stropy 1NP', 'Stropy 2NP' nebo 'Střecha')
        """
        if floor == '1NP':
            self.panels = self.panels_1np.copy()
        elif floor == '2NP':
            self.panels = self.panels_2np.copy()
        elif floor == 'Stropy 1NP':
            self.panels = self.stropy_1np.copy()
        elif floor == 'Stropy 2NP':
            self.panels = self.stropy_2np.copy()
        elif floor == 'Střecha':
            self.panels = self.strecha.copy()


class ConstructionFrame(QGraphicsRectItem):
    """
    Třída reprezentující rámeček konstrukce.
    """
    def __init__(self, width, height):
        """
        Inicializace rámečku konstrukce.

        Args:
            width (float): Šířka rámečku v mm
            height (float): Výška rámečku v mm
        """
        # Vytvoříme obdélník s danou šířkou a výškou
        super().__init__(0, 0, height, width)  # Otočeno o 90 stupňů - prohozená šířka a výška

        # Nastavíme vzhled rámečku
        self.setPen(QPen(QColor(0, 0, 0), 2))  # Černý okraj, tloušťka 2
        self.setBrush(QBrush(QColor(240, 240, 240)))  # Světle šedé pozadí

        # Nastavíme Z-hodnotu tak, aby byl rámeček pod panely
        self.setZValue(-1)

        # Nastavíme, že rámeček je možné vybrat a přesouvat
        self.setFlag(QGraphicsRectItem.GraphicsItemFlag.ItemIsSelectable, True)
        self.setFlag(QGraphicsRectItem.GraphicsItemFlag.ItemIsMovable, True)
        self.setFlag(QGraphicsRectItem.GraphicsItemFlag.ItemSendsGeometryChanges, True)
        self.setAcceptHoverEvents(True)

        # Uložíme rozměry
        self.frame_width = width
        self.frame_height = height

        # Proměnná pro sledování, zda se rámeček právě přesouvá
        self.is_moving = False

    def collidesWithItem(self, other, mode=Qt.ItemSelectionMode.IntersectsItemShape):
        """
        Přepsaná metoda pro detekci kolizí.
        Rámeček nikdy nekoliduje s jinými položkami.

        Args:
            other: Jiná položka
            mode: Mód detekce kolizí

        Returns:
            bool: Vždy False - rámeček nikdy nekoliduje
        """
        return False

    def hoverEnterEvent(self, event):
        """Změna kurzoru při najetí na rámeček."""
        self.setCursor(Qt.CursorShape.SizeAllCursor)
        super().hoverEnterEvent(event)

    def hoverLeaveEvent(self, event):
        """Resetování kurzoru při opuštění rámečku."""
        self.setCursor(Qt.CursorShape.ArrowCursor)
        super().hoverLeaveEvent(event)

    def mousePressEvent(self, event):
        """Zpracování události stisknutí tlačítka myši."""
        # Označíme, že se rámeček začíná přesouvat
        self.is_moving = True

        # Uložíme počáteční pozici rámečku
        self.start_pos = self.pos()

        # Uložíme počáteční pozici myši
        self.start_mouse_pos = event.scenePos()

        # Získáme obdélník rámečku v souřadnicích scény
        frame_rect = self.sceneBoundingRect()

        # Najdeme všechny panely ve scéně, které jsou uvnitř rámečku
        self.panels_to_move = []
        self.panel_start_positions = []

        if self.scene():
            for item in self.scene().items():
                if isinstance(item, Panel) and frame_rect.contains(item.sceneBoundingRect()):
                    self.panels_to_move.append(item)
                    self.panel_start_positions.append(item.pos())

        # Předáme událost nadřazené třídě
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Zpracování události pohybu myši."""
        if self.is_moving and self.panels_to_move:
            # Vypočítáme posun myši od počáteční pozice
            current_mouse_pos = event.scenePos()
            delta_mouse = current_mouse_pos - self.start_mouse_pos

            # Přesuneme všechny panely o stejný posun
            for i, panel in enumerate(self.panels_to_move):
                new_pos = self.panel_start_positions[i] + delta_mouse
                panel.setPos(new_pos)

        # Předáme událost nadřazené třídě
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """Zpracování události uvolnění tlačítka myši."""
        # Označíme, že se rámeček přestal přesouvat
        self.is_moving = False

        # Vyčistíme seznamy
        self.panels_to_move = []
        self.panel_start_positions = []

        # Předáme událost nadřazené třídě
        super().mouseReleaseEvent(event)

    def itemChange(self, change, value):
        """
        Zpracování změn položky.

        Args:
            change: Typ změny
            value: Nová hodnota

        Returns:
            Upravená hodnota
        """
        # Použijeme výchozí chování
        return super().itemChange(change, value)

    def paint(self, painter, option=None, widget=None):
        """
        Vykreslení rámečku.

        Args:
            painter (QPainter): Objekt pro kreslení
            option: Možnosti stylu (nepoužito)
            widget: Widget, na který se kreslí (nepoužito)
        """
        # Vykreslíme obdélník
        painter.setPen(self.pen())
        painter.setBrush(self.brush())
        painter.drawRect(self.rect())

        # Vykreslíme popisek s rozměry
        painter.setPen(QPen(QColor(0, 0, 0)))
        painter.setFont(painter.font())

        # Připravíme text s rozměry
        dimensions_text = f"{int(self.frame_width)}×{int(self.frame_height)}"

        # Vykreslíme text ve středu rámečku
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, dimensions_text)


class LayoutManager:
    """
    Třída pro správu ukládání a načítání rozvržení panelů.
    """
    def __init__(self):
        """Inicializace správce rozvržení."""
        self.current_layout_name = "default"

    def save_layout(self, panels, filename=None):
        """
        Uloží aktuální rozvržení panelů do souboru.

        Args:
            panels (list): Seznam panelů k uložení
            filename (str, optional): Název souboru. Pokud není zadán, použije se aktuální název.

        Returns:
            bool: True pokud bylo ukládání úspěšné, jinak False
        """
        import json
        import os

        # Pokud není zadán název souboru, použijeme aktuální název
        if filename is None:
            filename = self.current_layout_name

        # Přidáme příponu .json, pokud ji název neobsahuje
        if not filename.endswith('.json'):
            filename += '.json'

        # Vytvoříme adresář pro ukládání, pokud neexistuje
        save_dir = 'layouts'
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # Sestavíme cestu k souboru
        filepath = os.path.join(save_dir, filename)

        # Připravíme data k uložení
        layout_data = []
        for panel in panels:
            panel_data = {
                'id': panel.panel_id,
                'x': panel.pos().x(),
                'y': panel.pos().y(),
                'width': panel.original_width,
                'height': panel.original_height,
                'duplicate_level': panel.duplicate_level,
                'order': panel.order
            }
            layout_data.append(panel_data)

        try:
            # Uložíme data do souboru
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(layout_data, f, indent=2)

            # Aktualizujeme aktuální název rozvržení
            self.current_layout_name = filename.replace('.json', '')

            return True
        except Exception as e:
            print(f"Chyba při ukládání rozvržení: {e}")
            return False

    def load_layout(self, filename=None):
        """
        Načte rozvržení panelů ze souboru.

        Args:
            filename (str, optional): Název souboru. Pokud není zadán, použije se aktuální název.

        Returns:
            list: Seznam dat panelů, nebo None pokud načítání selhalo
        """
        import json
        import os

        # Pokud není zadán název souboru, použijeme aktuální název
        if filename is None:
            filename = self.current_layout_name

        # Přidáme příponu .json, pokud ji název neobsahuje
        if not filename.endswith('.json'):
            filename += '.json'

        # Sestavíme cestu k souboru
        filepath = os.path.join('layouts', filename)

        # Zkontrolujeme, zda soubor existuje
        if not os.path.exists(filepath):
            print(f"Soubor {filepath} neexistuje.")
            return None

        try:
            # Načteme data ze souboru
            with open(filepath, 'r', encoding='utf-8') as f:
                layout_data = json.load(f)

            # Aktualizujeme aktuální název rozvržení
            self.current_layout_name = filename.replace('.json', '')

            return layout_data
        except Exception as e:
            print(f"Chyba při načítání rozvržení: {e}")
            return None

    def get_available_layouts(self):
        """
        Získá seznam dostupných uložených rozvržení.

        Returns:
            list: Seznam názvů dostupných rozvržení
        """
        import os

        # Vytvoříme adresář pro ukládání, pokud neexistuje
        save_dir = 'layouts'
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # Získáme seznam souborů v adresáři
        files = [f for f in os.listdir(save_dir) if f.endswith('.json')]

        # Odstraníme příponu .json z názvů souborů
        layouts = [f.replace('.json', '') for f in files]

        return layouts
