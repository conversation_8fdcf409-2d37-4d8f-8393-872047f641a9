"""
Panel Planner Application

A tool for planning panel layouts in 2D space.
"""
import sys
from PyQt6.QtWidgets import QApplication
from controllers import MainController


def main():
    """Main entry point for the application."""
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Panel Planner")

    # Create and show main controller
    controller = MainController()
    controller.show()

    # Start event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
