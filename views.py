"""
Views for the Panel Planner application.
"""
from PyQt6.QtCore import Qt, QRectF, QPointF, pyqtSignal, QSizeF, QSize
from PyQt6.QtGui import QPainter, QColor, QPen, QBrush, QTransform, QKeyEvent, QPageLayout, QPageSize, QFont
from PyQt6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from PyQt6.QtWidgets import (
    QGraphicsView, QGraphicsScene, QListWidget, QListWidgetItem, QStyledItemDelegate, QStyle,
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSplitter, QTabWidget,
    QPushButton, QToolBar, QDialog, QDialogButtonBox, QComboBox, QFormLayout
)
from models import Panel, ConstructionFrame


class WorkspaceView(QGraphicsView):
    """
    Custom QGraphicsView for the workspace.
    """
    # Signal emitted when a panel is dropped from the library
    panel_dropped = pyqtSignal(str, QPointF)

    # Signal emitted when a panel should be removed
    panel_remove_requested = pyqtSignal(object)

    def __init__(self, parent=None):
        """Initialize the workspace view."""
        super().__init__(parent)

        # Create scene with extremely large dimensions - otočeno o 90 stupňů
        # Použijeme symetrické rozměry, aby byl pohyb možný ve všech směrech
        self.scene = QGraphicsScene(-100000, -100000, 200000, 200000)
        self.setScene(self.scene)

        # Setup view properties
        self.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.setDragMode(QGraphicsView.DragMode.RubberBandDrag)
        self.setViewportUpdateMode(QGraphicsView.ViewportUpdateMode.FullViewportUpdate)
        self.setTransformationAnchor(QGraphicsView.ViewportAnchor.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.ViewportAnchor.AnchorUnderMouse)

        # Nastavíme sceneRect na extrémně velké hodnoty, aby byl pohyb možný ve všech směrech
        # Důležité: sceneRect musí být větší než rozměry scény
        self.setSceneRect(-200000, -200000, 400000, 400000)
        self.scale(0.05, 0.05)  # Menší počáteční zoom pro lepší přehled

        # Setup grid
        self.grid_size = 50
        self.grid_color = QColor(200, 200, 200, 100)
        self.grid_pen = QPen(self.grid_color, 1, Qt.PenStyle.SolidLine)

        # Setup for drag and drop
        self.setAcceptDrops(True)

        # Inicializace proměnných pro panning
        self._panning = False
        self._pan_start_pos = None
        self._pan_start_scene_pos = None

        # Add construction area
        self.add_construction_area()

    def add_construction_area(self):
        """Add a construction area to the scene."""
        # Create a gray rectangle representing the construction area
        # Otočeno o 90 stupňů - výška a šířka jsou prohozeny
        construction_width = 13000
        construction_height = 500

        # Umístění konstrukce do středu scény
        scene_center_x = self.scene.width() / 2
        scene_center_y = self.scene.height() / 2

        construction_x = scene_center_x - construction_width / 2
        construction_y = scene_center_y - construction_height / 2

        construction_rect = QRectF(construction_x, construction_y,
                                  construction_width, construction_height)

        construction_brush = QBrush(QColor(150, 150, 150, 100))
        construction_pen = QPen(QColor(100, 100, 100), 2)

        self.construction_area = self.scene.addRect(
            construction_rect, construction_pen, construction_brush)

        # Uložíme střed konstrukce pro pozdější použití
        self.construction_center = QPointF(
            construction_x + construction_width / 2,
            construction_y + construction_height / 2
        )

        # Přidáme rámeček konstrukce o rozměrech 2500x13000
        frame_width = 2500
        frame_height = 13000

        # Vytvoříme rámeček
        self.construction_frame = ConstructionFrame(frame_width, frame_height)

        # Umístíme rámeček pod konstrukci
        frame_x = construction_x - (frame_height - construction_width) / 2
        frame_y = construction_y - (frame_width - construction_height) / 2

        self.construction_frame.setPos(frame_x, frame_y)

        # Přidáme rámeček do scény (před konstrukci, aby byl pod ní)
        self.scene.addItem(self.construction_frame)

    def add_new_construction(self):
        """Add a new construction area to the scene."""
        # Získáme aktuální pozici pohledu
        view_center = self.mapToScene(self.viewport().rect().center())

        # Create a gray rectangle representing the construction area
        # Otočeno o 90 stupňů - výška a šířka jsou prohozeny
        construction_width = 13000
        construction_height = 500

        # Umístění konstrukce na aktuální pozici pohledu
        construction_x = view_center.x() - construction_width / 2
        construction_y = view_center.y() - construction_height / 2

        construction_rect = QRectF(construction_x, construction_y,
                                  construction_width, construction_height)

        construction_brush = QBrush(QColor(150, 150, 150, 100))
        construction_pen = QPen(QColor(100, 100, 100), 2)

        # Přidáme rámeček konstrukce o rozměrech 2500x13000
        frame_width = 2500
        frame_height = 13000

        # Vytvoříme rámeček
        new_frame = ConstructionFrame(frame_width, frame_height)

        # Umístíme rámeček pod konstrukci
        frame_x = construction_x - (frame_height - construction_width) / 2
        frame_y = construction_y - (frame_width - construction_height) / 2

        new_frame.setPos(frame_x, frame_y)

        # Přidáme rámeček do scény
        self.scene.addItem(new_frame)

        # Přidáme novou konstrukci do scény
        self.scene.addRect(construction_rect, construction_pen, construction_brush)

    def drawBackground(self, painter, rect):
        """
        Draw custom background with grid.

        Args:
            painter (QPainter): Painter object
            rect (QRectF): Visible rectangle
        """
        # Call parent method first
        super().drawBackground(painter, rect)

        # Set background color
        self.setBackgroundBrush(QBrush(QColor(240, 240, 240)))

        # We'll implement a simpler grid in a future version

    def wheelEvent(self, event):
        """
        Handle mouse wheel events for zooming.

        Args:
            event (QWheelEvent): Wheel event
        """
        # Calculate zoom factor
        zoom_factor = 1.2
        if event.angleDelta().y() < 0:
            zoom_factor = 1.0 / zoom_factor

        # Apply zoom
        self.scale(zoom_factor, zoom_factor)

    def mousePressEvent(self, event):
        """
        Handle mouse press events.

        Args:
            event (QMouseEvent): Mouse event
        """
        # Middle button or right button panning
        if event.button() == Qt.MouseButton.MiddleButton or event.button() == Qt.MouseButton.RightButton:
            # Uložíme původní pozici kurzoru a scény
            self._pan_start_pos = event.position()
            self._pan_start_scene_pos = self.mapToScene(event.position().toPoint())
            self._panning = True
            self.setCursor(Qt.CursorShape.ClosedHandCursor)
            event.accept()
        else:
            self._panning = False
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """
        Handle mouse move events.

        Args:
            event (QMouseEvent): Mouse event
        """
        if self._panning:
            # Vypočítáme novou pozici scény na základě pohybu myši
            # Tento přístup je mnohem stabilnější než použití translate()

            # Získáme aktuální pozici myši
            new_pos = event.position()

            # Vypočítáme, kam by se měl posunout bod, který byl původně pod kurzorem
            new_scene_pos = self.mapToScene(new_pos.toPoint())

            # Vypočítáme rozdíl v souřadnicích scény
            delta_scene = new_scene_pos - self._pan_start_scene_pos

            # Posuneme pohled tak, aby bod pod kurzorem zůstal na stejném místě
            # Toto je klíčový krok pro stabilní posun
            self.centerOn(self.mapToScene(self.viewport().rect().center()) - delta_scene)

            # Aktualizujeme počáteční pozici scény pro další pohyb
            self._pan_start_scene_pos = self.mapToScene(new_pos.toPoint())

            event.accept()
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """
        Handle mouse release events.

        Args:
            event (QMouseEvent): Mouse event
        """
        # Reset po uvolnění prostředního nebo pravého tlačítka
        if event.button() == Qt.MouseButton.MiddleButton or event.button() == Qt.MouseButton.RightButton:
            self._panning = False
            self.setCursor(Qt.CursorShape.ArrowCursor)
            event.accept()
        else:
            super().mouseReleaseEvent(event)

    def dragEnterEvent(self, event):
        """
        Handle drag enter events.

        Args:
            event (QDragEnterEvent): Drag enter event
        """
        if event.mimeData().hasText():
            event.acceptProposedAction()

    def dragMoveEvent(self, event):
        """
        Handle drag move events.

        Args:
            event (QDragMoveEvent): Drag move event
        """
        if event.mimeData().hasText():
            event.acceptProposedAction()

    def dropEvent(self, event):
        """
        Handle drop events.

        Args:
            event (QDropEvent): Drop event
        """
        if event.mimeData().hasText():
            # Get panel ID from mime data
            panel_id = event.mimeData().text()

            # Convert drop position to scene coordinates
            drop_pos = self.mapToScene(event.position().toPoint())

            # Emit signal with panel ID and position
            self.panel_dropped.emit(panel_id, drop_pos)

            event.acceptProposedAction()

    def add_panel(self, panel):
        """
        Add a panel to the scene.

        Args:
            panel (Panel): Panel to add
        """
        self.scene.addItem(panel)

    def remove_panel(self, panel):
        """
        Remove a panel from the scene.

        Args:
            panel (Panel): Panel to remove
        """
        self.scene.removeItem(panel)

    def keyPressEvent(self, event):
        """
        Handle key press events.

        Args:
            event (QKeyEvent): Key event
        """
        # Pokud je stisknuta klávesa Delete
        if event.key() == Qt.Key.Key_Delete:
            # Získáme vybrané položky
            selected_items = self.scene.selectedItems()

            # Pokud jsou nějaké položky vybrány
            if selected_items:
                # Pro každou vybranou položku
                for item in selected_items:
                    # Emitujeme signál pro odstranění panelu
                    self.panel_remove_requested.emit(item)

                # Označíme událost jako zpracovanou
                event.accept()
                return

        # Pro ostatní klávesy použijeme výchozí chování
        super().keyPressEvent(event)

    def print_layout(self, printer):
        """
        Vytiskne aktuální rozvržení.

        Args:
            printer (QPrinter): Tiskárna
        """
        # Vytvoříme painter pro tisk
        painter = QPainter(printer)

        # Nastavíme vysokou kvalitu tisku
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QPainter.RenderHint.TextAntialiasing)
        painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)

        # Získáme obdélník scény, který obsahuje všechny položky
        scene_rect = self.scene.itemsBoundingRect()

        # Přidáme okraje
        margin = 10
        scene_rect.adjust(-margin, -margin, margin, margin)

        # Získáme velikost stránky
        page_rect = printer.pageRect(QPrinter.Unit.DevicePixel)

        # Vypočítáme měřítko pro přizpůsobení scény na stránku
        scale_x = page_rect.width() / scene_rect.width()
        scale_y = page_rect.height() / scene_rect.height()
        scale = min(scale_x, scale_y)

        # Nastavíme transformaci pro přizpůsobení scény na stránku
        painter.scale(scale, scale)
        painter.translate(-scene_rect.left(), -scene_rect.top())

        # Vykreslíme scénu
        self.scene.render(painter, QRectF(), scene_rect)

        # Ukončíme kreslení
        painter.end()


class PanelItemDelegate(QStyledItemDelegate):
    """
    Vlastní delegát pro vykreslování položek v seznamu panelů.
    """
    def paint(self, painter, option, index):
        """
        Vykreslí položku v seznamu.

        Args:
            painter (QPainter): Objekt pro kreslení
            option (QStyleOptionViewItem): Možnosti stylu
            index (QModelIndex): Index položky
        """
        # Vykreslíme pozadí a text pomocí výchozího stylu
        super().paint(painter, option, index)


class LibraryView(QListWidget):
    """
    Custom QListWidget for the panel library.
    """
    def __init__(self, parent=None):
        """Initialize the library view."""
        super().__init__(parent)

        # Setup drag and drop
        self.setDragEnabled(True)

        # Nastavíme vlastní delegát pro vykreslování položek
        self.setItemDelegate(PanelItemDelegate())

        # Sledování použitých panelů
        self.used_panels = set()

    def add_panel(self, panel_id, width, height, order=None):
        """
        Add a panel to the library.

        Args:
            panel_id (str): Panel ID
            width (float): Panel width
            height (float): Panel height
            order (int, optional): Pořadí panelu (zobrazeno červeně)
        """
        # Create list item with panel info
        if order is not None:
            # Pokud má panel pořadí, přidáme ho na začátek textu s tečkou
            item = QListWidgetItem(f"{order}. {panel_id} ({width}×{height})")
        else:
            item = QListWidgetItem(f"{panel_id} ({width}×{height})")

        # Uložíme ID a pořadí panelu do dat položky
        item.setData(Qt.ItemDataRole.UserRole, panel_id)
        item.setData(Qt.ItemDataRole.UserRole + 1, order)

        # Add to list
        self.addItem(item)

    def mark_panel_as_used(self, panel_id, duplicate_level=0):
        """
        Označí panel v knihovně jako použitý.

        Args:
            panel_id (str): ID panelu, který byl použit
            duplicate_level (int): Úroveň duplikace (0=originál, 1=první duplikát, 2+=další duplikáty)
        """
        # Přidáme panel do seznamu použitých
        self.used_panels.add(panel_id)

        # Projdeme všechny položky v knihovně
        for i in range(self.count()):
            item = self.item(i)
            item_panel_id = item.data(Qt.ItemDataRole.UserRole)

            # Pokud je položka použitý panel, změníme její vzhled
            if item_panel_id == panel_id:
                font = item.font()
                font.setItalic(True)  # Kurzíva pro všechny použité panely

                if duplicate_level >= 2:
                    # Nastavíme fialové pozadí a tučné písmo pro další duplikáty
                    item.setBackground(QColor(255, 150, 255))  # Fialové pozadí
                    font.setBold(True)    # Tučné písmo pro zvýraznění
                    item.setFont(font)
                elif duplicate_level == 1:
                    # Nastavíme oranžové pozadí a tučné písmo pro první duplikát
                    item.setBackground(QColor(255, 200, 100))  # Oranžové pozadí
                    font.setBold(True)    # Tučné písmo pro zvýraznění
                    item.setFont(font)
                else:
                    # Nastavíme šedé pozadí a normální písmo pro použité panely
                    item.setBackground(QColor(200, 200, 200))  # Šedé pozadí
                    font.setBold(False)   # Normální tloušťka písma
                    item.setFont(font)
                break

    def unmark_panel_as_used(self, panel_id):
        """
        Odznačí panel v knihovně jako použitý.

        Args:
            panel_id (str): ID panelu, který již není použitý
        """
        # Odstraníme panel ze seznamu použitých
        if panel_id in self.used_panels:
            self.used_panels.remove(panel_id)

        # Projdeme všechny položky v knihovně
        for i in range(self.count()):
            item = self.item(i)
            item_panel_id = item.data(Qt.ItemDataRole.UserRole)

            # Pokud je položka panel, který již není použitý, obnovíme její vzhled
            if item_panel_id == panel_id:
                # Obnovíme původní vzhled
                item.setBackground(QColor(255, 255, 255))  # Bílé pozadí
                font = item.font()
                font.setItalic(False)  # Normální písmo
                item.setFont(font)
                break

    def startDrag(self, supported_actions):
        """
        Start drag operation.

        Args:
            supported_actions: Supported drag actions
        """
        # Get selected item
        item = self.currentItem()
        if not item:
            return

        # Get panel ID from item data
        panel_id = item.data(Qt.ItemDataRole.UserRole)

        # Create drag object
        from PyQt6.QtGui import QDrag
        from PyQt6.QtCore import QMimeData

        drag = QDrag(self)
        mime_data = QMimeData()
        mime_data.setText(panel_id)
        drag.setMimeData(mime_data)

        # Start drag operation
        drag.exec(supported_actions)


class PrintSettingsDialog(QDialog):
    """
    Dialog pro nastavení tisku.
    """
    def __init__(self, parent=None):
        """Inicializace dialogu pro nastavení tisku."""
        super().__init__(parent)

        self.setWindowTitle("Nastavení tisku")
        self.setMinimumWidth(300)

        # Vytvoříme layout
        layout = QFormLayout(self)

        # Přidáme výběr orientace stránky
        self.orientation_combo = QComboBox()
        self.orientation_combo.addItem("Na výšku", QPageLayout.Orientation.Portrait)
        self.orientation_combo.addItem("Na šířku", QPageLayout.Orientation.Landscape)
        layout.addRow("Orientace:", self.orientation_combo)

        # Přidáme výběr velikosti stránky
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItem("A4", QPageSize.PageSizeId.A4)
        self.page_size_combo.addItem("A3", QPageSize.PageSizeId.A3)
        self.page_size_combo.addItem("A2", QPageSize.PageSizeId.A2)
        self.page_size_combo.addItem("A1", QPageSize.PageSizeId.A1)
        self.page_size_combo.addItem("A0", QPageSize.PageSizeId.A0)
        layout.addRow("Velikost stránky:", self.page_size_combo)

        # Přidáme tlačítka
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addRow(button_box)

    def get_settings(self):
        """
        Získá nastavení tisku.

        Returns:
            tuple: (orientace, velikost stránky)
        """
        orientation = self.orientation_combo.currentData()
        page_size = self.page_size_combo.currentData()
        return orientation, page_size


class MainView(QWidget):
    """
    Main view containing workspace and library.
    """
    # Signály pro ukládání, načítání a přidání nové konstrukce
    save_layout_requested = pyqtSignal()
    load_layout_requested = pyqtSignal()
    print_layout_requested = pyqtSignal()
    add_construction_requested = pyqtSignal()

    def __init__(self, parent=None):
        """Initialize the main view."""
        super().__init__(parent)

        # Create layout
        self.layout = QVBoxLayout(self)
        self.content_layout = QVBoxLayout()
        self.layout.addLayout(self.content_layout)

        # Create tab widget
        self.tab_widget = QTabWidget()
        self.layout.addWidget(self.tab_widget)

        # Create tab widget
        self.tab_widget = QTabWidget()
        self.layout.addWidget(self.tab_widget)

        # Create toolbar
        self.toolbar = QToolBar()
        self.layout.addWidget(self.toolbar)

        # Create library for Panely 2část
        self.library_2cast = LibraryView()
        self.tab_widget.addTab(self.library_2cast, "Panely 2část")

        # Add save button
        self.save_button = QPushButton("Uložit")
        self.save_button.clicked.connect(self.save_layout_requested.emit)
        self.toolbar.addWidget(self.save_button)

        # Add load button
        self.load_button = QPushButton("Načíst")
        self.load_button.clicked.connect(self.load_layout_requested.emit)
        self.toolbar.addWidget(self.load_button)

        # Add print button
        self.print_button = QPushButton("Tisk")
        self.print_button.clicked.connect(self.print_layout_requested.emit)
        self.toolbar.addWidget(self.print_button)

        # Add construction button
        self.add_construction_button = QPushButton("Přidat konstrukci")
        self.add_construction_button.clicked.connect(self.add_construction_requested.emit)
        self.toolbar.addWidget(self.add_construction_button)

        # Create main content area
        self.content_widget = QWidget()
        self.content_layout = QHBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.layout.addWidget(self.content_widget)

        # Create splitter for resizable panels
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        self.content_layout.addWidget(self.splitter)

        # Create workspace view
        self.workspace = WorkspaceView()
        #self.splitter.addWidget(self.workspace)

        # Create library panel
        self.library_panel = QWidget()
        self.library_layout = QVBoxLayout(self.library_panel)

        # Add library label
        self.library_label = QLabel("Panel Library")
        self.library_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.library_layout.addWidget(self.library_label)

        # Vytvoříme záložky pro patra
        self.floor_tabs = QTabWidget()
        self.library_layout.addWidget(self.floor_tabs)

        # Vytvoříme záložku pro 1NP
        self.tab_1np = QWidget()
        self.tab_1np_layout = QVBoxLayout(self.tab_1np)
        self.library_1np = LibraryView()
        self.tab_1np_layout.addWidget(self.library_1np)
        self.floor_tabs.addTab(self.tab_1np, "1NP")

        # Vytvoříme záložku pro 2NP
        self.tab_2np = QWidget()
        self.tab_2np_layout = QVBoxLayout(self.tab_2np)
        self.library_2np = LibraryView()
        self.tab_2np_layout.addWidget(self.library_2np)
        self.floor_tabs.addTab(self.tab_2np, "2NP")

        # Vytvoříme záložku pro Stropy 1NP
        self.tab_stropy = QWidget()
        self.tab_stropy_layout = QVBoxLayout(self.tab_stropy)
        self.library_stropy = LibraryView()
        self.tab_stropy_layout.addWidget(self.library_stropy)
        self.floor_tabs.addTab(self.tab_stropy, "Stropy 1NP")

        # Vytvoříme záložku pro Stropy 2NP
        self.tab_stropy2 = QWidget()
        self.tab_stropy2_layout = QVBoxLayout(self.tab_stropy2)
        self.library_stropy2 = LibraryView()
        self.tab_stropy2_layout.addWidget(self.library_stropy2)
        self.floor_tabs.addTab(self.tab_stropy2, "Stropy 2NP")

        # Vytvoříme záložku pro Střechu
        self.tab_strecha = QWidget()
        self.tab_strecha_layout = QVBoxLayout(self.tab_strecha)
        self.library_strecha = LibraryView()
        self.tab_strecha_layout.addWidget(self.library_strecha)
        self.floor_tabs.addTab(self.tab_strecha, "Střecha")

        # Nastavíme výchozí knihovnu (pro zpětnou kompatibilitu)
        self.library = self.library_1np

        # Připojíme signál pro změnu záložky
        self.floor_tabs.currentChanged.connect(self.on_tab_changed)

        # Add library panel to splitter
        self.splitter.addWidget(self.workspace)

        self.splitter.addWidget(self.library_panel)
        # Set splitter proportions (75% workspace, 25% library)
        self.splitter.setSizes([3000, 1000])

    def on_tab_changed(self, index):
        """
        Zpracuje změnu záložky v knihovně.

        Args:
            index (int): Index vybrané záložky
        """
        if index == 0:  # 1NP
            self.library = self.library_1np
        elif index == 1:  # 2NP
            self.library = self.library_2np
        elif index == 2:  # Stropy 1NP
            self.library = self.library_stropy
        elif index == 3:  # Stropy 2NP
            self.library = self.library_stropy2
        elif index == 4:  # Střecha
            self.library = self.library_strecha
