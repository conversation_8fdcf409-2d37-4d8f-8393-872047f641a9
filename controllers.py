# PyQt6 is not installed. Please install it using: pip install PyQt6
"""
Controllers for the Panel Planner application.
"""
from PyQt6.QtCore import QObject, QTimer, QPointF
from PyQt6.QtWidgets import QInputDialog, QMessageBox
from PyQt6.QtPrintSupport import QPrin<PERSON>, QPrintDialog, QPrintPreviewDialog
from PyQt6.QtGui import QPageLayout, QPageSize

from models import PanelLibrary, LayoutManager
from views import MainView, PrintSettingsDialog


class MainController(QObject):
    """
    Main controller for the Panel Planner application.
    """
    def __init__(self):
        """Initialize the main controller."""
        super().__init__()

        # Create models
        self.panel_library = PanelLibrary()
        self.layout_manager = LayoutManager()

        # Create main view
        self.main_view = MainView()

        # Connect signals
        self.connect_signals()

        # Populate library
        self.populate_library()

        # Setup collision detection timer
        self.collision_timer = QTimer()
        self.collision_timer.timeout.connect(self.check_collisions)
        self.collision_timer.start(500)  # Check every 500ms

        # Track placed panels
        self.placed_panels = []

    def connect_signals(self):
        """Connect signals between components."""
        # Connect panel dropped signal
        self.main_view.workspace.panel_dropped.connect(self.on_panel_dropped)

        # Connect panel remove requested signal
        self.main_view.workspace.panel_remove_requested.connect(self.remove_panel)

        # Connect save and load signals
        self.main_view.save_layout_requested.connect(self.save_layout)
        self.main_view.load_layout_requested.connect(self.load_layout)

        # Connect print signal
        self.main_view.print_layout_requested.connect(self.print_layout)

        # Connect add construction signal
        self.main_view.add_construction_requested.connect(self.add_construction)

    def populate_library(self):
        """Populate the library with available panels."""
        # Pořadí panelů pro 2NP
        panel_order_2np = {
            "O2.01": 1, "O2.02": 2, "O2.03": 3, "O2.04": 4, "P2.05": 5,
            "P2.06": 6, "P2.07": 7, "P2.08": 8, "P2.09": 9, "P2.10": 10,
            "P2.11": 11, "P2.12": 12, "P2.13": 13, "O2.14": 14, "P2.15": 15,
            "P2.16": 16, "O2.17": 17, "P2.18": 18, "O2.19": 19, "P2.20": 20,
            "O2.21": 21, "O2.22": 22, "O3.01": 23, "O3.02": 24, "O3.03": 25
        }

        # Pořadí panelů pro Stropy 1NP
        stropy_order = {
            "ST-25": 1, "ST-24": 2, "ST-23": 3, "ST-22": 4, "ST-21": 5,
            "ST-20": 6, "ST-19": 7, "ST-18": 8, "ST-17": 9, "ST-26": 10,
            "ST-XX": 11
        }

        # Pořadí panelů pro Stropy 2NP
        stropy2_order = {
            "S-1": 1, "S-2": 2, "S-3": 3, "S-4": 4, "S-5": 5, "S-6": 6,
            "S-7": 7, "S-8": 8, "S-9": 9, "S-10": 10, "S-11": 11, "S-12": 12
        }

        # Pořadí panelů pro Střechu
        strecha_order = {
            "STR-1": 1, "STR-2": 2, "STR-3": 3, "STR-4": 4, "STR-5": 5, "STR-6": 6,
            "STR-7": 7, "STR-8": 8, "STR-9": 9, "STR-10": 10, "STR-11": 11, "STR-12": 12,
            "STR-13": 13, "STR-14": 14, "STR-15": 15, "STR-16": 16, "STR-17": 17, "STR-18": 18
        }

        # Pořadí panelů pro 1NP
        panel_order_1np = {
            "O1.08": 1, "O1.09": 2, "P1.69": 3, "P1.66": 4, "P1.62": 5,
            "P1.65": 6, "P1.64": 7, "P1.63": 8, "P1.61": 9, "O1.06": 10,
            "O1.05": 11, "O1.07": 12, "P1.70": 13, "P1.68": 14, "P1.67": 15,
            "P1.57": 16, "P1.36": 17, "P1.35": 18, "P1.34": 19, "P1.33": 20,
            "O1.10": 21, "O1.11": 22, "P1.58": 23, "P1.60": 24, "P1.71": 25,
            "P1.73": 26, "P1.74": 27, "P1.72": 28, "P1.75": 29, "P1.76": 30,
            "P1.59": 31, "P1.39": 32, "P1.40": 33
        }

        # Naplníme knihovnu pro 1NP
        for panel_id in self.panel_library.get_panel_ids('1NP'):
            width, height = self.panel_library.get_panel_dimensions(panel_id)
            # Přidáme pořadí panelu
            order = panel_order_1np.get(panel_id)
            self.main_view.library_1np.add_panel(panel_id, width, height, order)

        # Naplníme knihovnu pro 2NP
        for panel_id in self.panel_library.get_panel_ids('2NP'):
            width, height = self.panel_library.get_panel_dimensions(panel_id)
            # Přidáme pořadí panelu
            order = panel_order_2np.get(panel_id)
            self.main_view.library_2np.add_panel(panel_id, width, height, order)

        # Naplníme knihovnu pro Stropy 1NP
        for panel_id in self.panel_library.get_panel_ids('Stropy 1NP'):
            width, height = self.panel_library.get_panel_dimensions(panel_id)
            # Přidáme pořadí panelu
            order = stropy_order.get(panel_id)
            self.main_view.library_stropy.add_panel(panel_id, width, height, order)

        # Naplníme knihovnu pro Stropy 2NP
        for panel_id in self.panel_library.get_panel_ids('Stropy 2NP'):
            width, height = self.panel_library.get_panel_dimensions(panel_id)
            # Přidáme pořadí panelu
            order = stropy2_order.get(panel_id)
            self.main_view.library_stropy2.add_panel(panel_id, width, height, order)

        # Naplníme knihovnu pro Střechu
        for panel_id in self.panel_library.get_panel_ids('Střecha'):
            width, height = self.panel_library.get_panel_dimensions(panel_id)
            # Přidáme pořadí panelu
            order = strecha_order.get(panel_id)
            self.main_view.library_strecha.add_panel(panel_id, width, height, order)

        # Naplníme knihovnu pro Panely 2část
        for panel_id in self.panel_library.get_panel_ids('Panely 2část'):
            width, height = self.panel_library.get_panel_dimensions(panel_id)
            # Přidáme pořadí panelu
            # Pro "Panely 2část" nemáme definované pořadí, takže nastavíme None
            panel_order_2cast = {
                "P1-24": 12, "P1-25": 13, "P1-26": 11, "P1-27": 9, "P1-28": 10,
                "P1-29": 8, "P1-30": 7, "P1-31": 6, "P1-32": 5, "P1-37": 14,
                "P1-38": 15, "P1-41": 16, "P1-42": 17, "P1-43": 27, "P1-44": 28,
                "P1-45": 18, "P1-46": 26, "P1-47": 20, "P1-49": 21, "P1-50": 24,
                "P1-51": 25, "P1-52": 23, "P1-53": 22, "P1-54": 29, "P1-55": 19,
                "P1-56": 30, "O1-04": 2, "O1-03": 1, "O1-02": 3, "O1-01": 4,
                "O1-23": 31, "O1-22": 32, "O1-21": 33
            }
            order = panel_order_2cast.get(panel_id)
            self.main_view.library_2cast.add_panel(panel_id, width, height, order)

    def on_panel_dropped(self, panel_id, position):
        """
        Handle panel dropped from library to workspace.

        Args:
            panel_id (str): ID of the dropped panel
            position (QPointF): Drop position in scene coordinates
        """
        # Spočítáme, kolik instancí tohoto panelu již existuje
        panel_count = 0
        for p in self.placed_panels:
            if p.panel_id == panel_id:
                panel_count += 1

        # Určíme úroveň duplikace
        duplicate_level = panel_count

        # Získáme pořadí panelu
        order = None
        # Pořadí panelů pro 1NP
        panel_order_1np = {
            "O1.08": 1, "O1.09": 2, "P1.69": 3, "P1.66": 4, "P1.62": 5,
            "P1.65": 6, "P1.64": 7, "P1.63": 8, "P1.61": 9, "O1.06": 10,
            "O1.05": 11, "O1.07": 12, "P1.70": 13, "P1.68": 14, "P1.67": 15,
            "P1.57": 16, "P1.36": 17, "P1.35": 18, "P1.34": 19, "P1.33": 20,
            "O1.10": 21, "O1.11": 22, "P1.58": 23, "P1.60": 24, "P1.71": 25,
            "P1.73": 26, "P1.74": 27, "P1.72": 28, "P1.75": 29, "P1.76": 30,
            "P1.59": 31, "P1.39": 32, "P1.40": 33
        }

        # Pořadí panelů pro 2NP
        panel_order_2np = {
            "O2.01": 1, "O2.02": 2, "O2.03": 3, "O2.04": 4, "P2.05": 5,
            "P2.06": 6, "P2.07": 7, "P2.08": 8, "P2.09": 9, "P2.10": 10,
            "P2.11": 11, "P2.12": 12, "P2.13": 13, "O2.14": 14, "P2.15": 15,
            "P2.16": 16, "O2.17": 17, "P2.18": 18, "O2.19": 19, "P2.20": 20,
            "O2.21": 21, "O2.22": 22, "O3.01": 23, "O3.02": 24, "O3.03": 25
        }
        # Pořadí panelů pro Stropy 1NP
        stropy_order = {
            "ST-25": 1, "ST-24": 2, "ST-23": 3, "ST-22": 4, "ST-21": 5,
            "ST-20": 6, "ST-19": 7, "ST-18": 8, "ST-17": 9, "ST-26": 10,
            "ST-XX": 11
        }

        # Pořadí panelů pro Stropy 2NP
        stropy2_order = {
            "S-1": 1, "S-2": 2, "S-3": 3, "S-4": 4, "S-5": 5, "S-6": 6,
            "S-7": 7, "S-8": 8, "S-9": 9, "S-10": 10, "S-11": 11, "S-12": 12
        }

        # Pořadí panelů pro Střechu
        strecha_order = {
            "STR-1": 1, "STR-2": 2, "STR-3": 3, "STR-4": 4, "STR-5": 5, "STR-6": 6,
            "STR-7": 7, "STR-8": 8, "STR-9": 9, "STR-10": 10, "STR-11": 11, "STR-12": 12,
            "STR-13": 13, "STR-14": 14, "STR-15": 15, "STR-16": 16, "STR-17": 17, "STR-18": 18
        }

        if panel_id in panel_order_1np:
            order = panel_order_1np[panel_id]
        elif panel_id in panel_order_2np:
            order = panel_order_2np[panel_id]
        elif panel_id in stropy_order:
            order = stropy_order[panel_id]
        elif panel_id in stropy2_order:
            order = stropy2_order[panel_id]
        elif panel_id in strecha_order:
            order = strecha_order[panel_id]
        else:
            panel_order_2cast = {
                "P1-24": 12, "P1-25": 13, "P1-26": 11, "P1-27": 9, "P1-28": 10,
                "P1-29": 8, "P1-30": 7, "P1-31": 6, "P1-32": 5, "P1-37": 14,
                "P1-38": 15, "P1-41": 16, "P1-42": 17, "P1-43": 27, "P1-44": 28,
                "P1-45": 18, "P1-46": 26, "P1-47": 20, "P1-49": 21, "P1-50": 24,
                "P1-51": 25, "P1-52": 23, "P1-53": 22, "P1-54": 29, "P1-55": 19,
                "P1-56": 30, "O1-04": 2, "O1-03": 1, "O1-02": 3, "O1-01": 4,
                "O1-23": 31, "O1-22": 32, "O1-21": 33
            }
            if panel_id in panel_order_2cast:
                order = panel_order_2cast[panel_id]

        # Create new panel
        panel = self.panel_library.create_panel(panel_id, duplicate_level)
        if not panel:
            return

        # Nastavíme pořadí panelu
        panel.order = order

        # Center panel on drop position
        panel_width = panel.rect().width()
        panel_height = panel.rect().height()
        centered_pos = QPointF(
            position.x() - panel_width / 2,
            position.y() - panel_height / 2
        )
        panel.setPos(centered_pos)

        # Add panel to workspace
        self.main_view.workspace.add_panel(panel)

        # Add to tracked panels
        self.placed_panels.append(panel)

        # Označíme (vybereme) nově přidaný panel
        panel.setSelected(True)

        # Zajistíme, že panel má focus
        panel.setFocus()

        # Označíme panel ve všech knihovnách jako použitý
        self.main_view.library_1np.mark_panel_as_used(panel_id, duplicate_level)
        self.main_view.library_2np.mark_panel_as_used(panel_id, duplicate_level)
        self.main_view.library_stropy.mark_panel_as_used(panel_id, duplicate_level)
        self.main_view.library_stropy2.mark_panel_as_used(panel_id, duplicate_level)
        self.main_view.library_strecha.mark_panel_as_used(panel_id, duplicate_level)
        self.main_view.library_2cast.mark_panel_as_used(panel_id, duplicate_level)

    def check_collisions(self):
        """Update panel appearance."""
        for panel in self.placed_panels:
            # Force update to refresh appearance
            panel.update()

    def remove_panel(self, panel):
        """
        Remove a panel from the workspace.

        Args:
            panel (Panel): Panel to remove
        """
        if panel in self.placed_panels:
            # Získáme ID panelu před odstraněním
            panel_id = panel.panel_id

            # Odstraníme panel z pracovního prostoru
            self.main_view.workspace.remove_panel(panel)

            # Odstraníme panel ze seznamu umístěných panelů
            self.placed_panels.remove(panel)

            # Spočítáme, kolik instancí tohoto panelu zůstalo a aktualizujeme jejich úrovně
            remaining_panels = []
            for p in self.placed_panels:
                if p.panel_id == panel_id:
                    remaining_panels.append(p)

            # Pokud již neexistují další instance tohoto panelu, odznačíme ho v knihovně
            if not remaining_panels:
                # Odznačíme panel ve všech knihovnách
                self.main_view.library_1np.unmark_panel_as_used(panel_id)
                self.main_view.library_2np.unmark_panel_as_used(panel_id)
                self.main_view.library_stropy.unmark_panel_as_used(panel_id)
                self.main_view.library_stropy2.unmark_panel_as_used(panel_id)
                self.main_view.library_strecha.unmark_panel_as_used(panel_id)
                self.main_view.library_2cast.unmark_panel_as_used(panel_id)
                self.main_view.library_strecha.unmark_panel_as_used(panel_id)
            else:
                # Aktualizujeme úrovně duplikace pro zbývající panely
                for i, p in enumerate(remaining_panels):
                    # Nastavíme novou úroveň duplikace
                    p.duplicate_level = i
                    p.is_duplicate = (i > 0)  # Pro zpětnou kompatibilitu
                    p.update()  # Vynutíme překreslení

                # Aktualizujeme stav v knihovně podle počtu zbývajících panelů
                duplicate_level = len(remaining_panels) - 1
                self.main_view.library_1np.mark_panel_as_used(panel_id, duplicate_level)
                self.main_view.library_2np.mark_panel_as_used(panel_id, duplicate_level)
                self.main_view.library_stropy.mark_panel_as_used(panel_id, duplicate_level)
                self.main_view.library_stropy2.mark_panel_as_used(panel_id, duplicate_level)
                self.main_view.library_strecha.mark_panel_as_used(panel_id, duplicate_level)

    def show(self):
        """Show the main view."""
        # Nastavení počáteční pozice pohledu na střed konstrukce
        self.main_view.workspace.centerOn(self.main_view.workspace.construction_center)
        self.main_view.show()

    def save_layout(self, filename=None):
        """
        Uloží aktuální rozvržení panelů do souboru.

        Args:
            filename (str, optional): Název souboru. Pokud není zadán, zobrazí se dialog pro zadání názvu.

        Returns:
            bool: True pokud bylo ukládání úspěšné, jinak False
        """
        # Pokud není zadán název souboru, zobrazíme dialog pro zadání názvu
        if filename is None:
            filename, ok = QInputDialog.getText(
                self.main_view,
                "Uložit rozvržení",
                "Zadejte název rozvržení:",
                text=self.layout_manager.current_layout_name
            )
            if not ok or not filename:
                return False

        # Uložíme rozvržení
        success = self.layout_manager.save_layout(self.placed_panels, filename)

        # Zobrazíme zprávu o výsledku
        if success:
            QMessageBox.information(
                self.main_view,
                "Uložení úspěšné",
                f"Rozvržení bylo úspěšně uloženo jako '{filename}'."
            )
        else:
            QMessageBox.warning(
                self.main_view,
                "Chyba při ukládání",
                f"Nepodařilo se uložit rozvržení '{filename}'."
            )

        return success

    def load_layout(self, filename=None):
        """
        Načte rozvržení panelů ze souboru.

        Args:
            filename (str, optional): Název souboru. Pokud není zadán, zobrazí se dialog pro výběr souboru.

        Returns:
            bool: True pokud bylo načítání úspěšné, jinak False
        """
        # Pokud není zadán název souboru, zobrazíme dialog pro výběr souboru
        if filename is None:
            # Získáme seznam dostupných rozvržení
            layouts = self.layout_manager.get_available_layouts()

            if not layouts:
                QMessageBox.information(
                    self.main_view,
                    "Žádná rozvržení",
                    "Nejsou k dispozici žádná uložená rozvržení."
                )
                return False

            # Zobrazíme dialog pro výběr rozvržení
            filename, ok = QInputDialog.getItem(
                self.main_view,
                "Načíst rozvržení",
                "Vyberte rozvržení:",
                layouts,
                0,
                False
            )
            if not ok or not filename:
                return False

        # Načteme data rozvržení
        layout_data = self.layout_manager.load_layout(filename)

        if layout_data is None:
            QMessageBox.warning(
                self.main_view,
                "Chyba při načítání",
                f"Nepodařilo se načíst rozvržení '{filename}'."
            )
            return False

        # Odstraníme všechny panely z pracovního prostoru
        for panel in self.placed_panels[:]:
            self.remove_panel(panel)

        # Vytvoříme nové panely podle načtených dat
        for panel_data in layout_data:
            # Vytvoříme panel
            panel_id = panel_data['id']
            duplicate_level = panel_data.get('duplicate_level', 0)
            panel = self.panel_library.create_panel(panel_id, duplicate_level)

            if panel:
                # Nastavíme pozici panelu
                panel.setPos(panel_data['x'], panel_data['y'])

                # Nastavíme pořadí panelu
                if 'order' in panel_data:
                    panel.order = panel_data['order']

                # Přidáme panel do pracovního prostoru
                self.main_view.workspace.add_panel(panel)

                # Přidáme panel do seznamu umístěných panelů
                self.placed_panels.append(panel)

                # Označíme panel ve všech knihovnách jako použitý
                self.main_view.library_1np.mark_panel_as_used(panel_id, duplicate_level)
                self.main_view.library_2np.mark_panel_as_used(panel_id, duplicate_level)
                self.main_view.library_stropy.mark_panel_as_used(panel_id, duplicate_level)
                self.main_view.library_stropy2.mark_panel_as_used(panel_id, duplicate_level)
                self.main_view.library_strecha.mark_panel_as_used(panel_id, duplicate_level)

        # Zobrazíme zprávu o úspěšném načtení
        QMessageBox.information(
            self.main_view,
            "Načtení úspěšné",
            f"Rozvržení '{filename}' bylo úspěšně načteno."
        )

        return True

    def add_construction(self):
        """
        Přidá novou konstrukci do pracovního prostoru.
        """
        # Přidáme novou konstrukci
        self.main_view.workspace.add_new_construction()

        # Zobrazíme zprávu o úspěšném přidání
        QMessageBox.information(
            self.main_view,
            "Konstrukce přidána",
            "Nová konstrukce byla úspěšně přidána do pracovního prostoru."
        )

    def print_layout(self):
        """
        Vytiskne aktuální rozvržení panelů.
        """
        # Zobrazíme dialog pro nastavení tisku
        settings_dialog = PrintSettingsDialog(self.main_view)
        if not settings_dialog.exec():
            return

        # Získáme nastavení tisku
        orientation, page_size = settings_dialog.get_settings()

        # Vytvoříme tiskárnu
        printer = QPrinter(QPrinter.PrinterMode.HighResolution)

        # Nastavíme orientaci a velikost stránky
        page_layout = printer.pageLayout()
        page_layout.setOrientation(orientation)
        page_layout.setPageSize(QPageSize(page_size))
        printer.setPageLayout(page_layout)

        # Zobrazíme dialog náhledu tisku
        preview_dialog = QPrintPreviewDialog(printer, self.main_view)
        preview_dialog.setWindowTitle("Náhled tisku")

        # Připojíme signál pro tisk
        preview_dialog.paintRequested.connect(self.main_view.workspace.print_layout)

        # Zobrazíme dialog
        preview_dialog.exec()
